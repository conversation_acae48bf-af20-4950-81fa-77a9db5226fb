import Hero from "../components/home/<USER>";
import Services from "../components/home/<USER>";
import PortalStatistics from "../components/home/<USER>";
// @ts-ignore
import { MapProvider } from "../components/mapFolder/MapContext.js";
import HomeLayout from "../components/layouts/HomeLayout";
import { useEffect, useRef, useState } from "react";
import { scrollToTop } from "../utils/helpers";
import Map2 from "../components/home/<USER>/Map2.js";
import BulletNavigation from "../components/common/BulletNavigation";
import Footer from "../components/Footer.js";
import { useTranslation } from "react-i18next";
import {
  FaHome,
  FaMap,
  FaCogs,
  FaChartBar,
  FaInfoCircle,
} from "react-icons/fa";

// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/mousewheel";

// import required modules
import { Pagination, Mousewheel, Keyboard } from "swiper/modules";

/**
 * HomePage component with vertical carousel
 * This page includes the Hero, Map, Services, and Statistics sections with bullet navigation
 */
export default function HomePage() {
  const { t } = useTranslation();
  const swiperRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState(0);

  // Scroll to top when the component mounts
  useEffect(() => {
    scrollToTop();

    // Add carousel-active class to body
    document.body.classList.add("carousel-active");

    return () => {
      // Remove carousel-active class when component unmounts
      document.body.classList.remove("carousel-active");
    };
  }, []);

  // Define sections for bullet navigation
  const bulletSections = [
    {
      id: "hero-section",
      label: t("navigation.home", "الرئيسية"),
      icon: <FaHome />,
    },
    {
      id: "map-section",
      label: t("navigation.map", "الخريطة"),
      icon: <FaMap />,
    },
    {
      id: "services-section",
      label: t("navigation.services", "الخدمات"),
      icon: <FaCogs />,
    },
    {
      id: "statistics-section",
      label: t("navigation.statistics", "الإحصائيات"),
      icon: <FaChartBar />,
    },
    {
      id: "footer-section",
      label: t("navigation.footer", "معلومات التواصل"),
      icon: <FaInfoCircle />,
    },
  ];

  // Handle bullet navigation click
  const handleBulletClick = (index: number) => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(index);
    }
  };

  // Handle slide change
  const handleSlideChange = (swiper: any) => {
    setActiveSlide(swiper.activeIndex);
  };

  return (
    <>
      {/* Bullet Navigation */}
      <BulletNavigation
        sections={bulletSections}
        activeIndex={activeSlide}
        onSectionClick={handleBulletClick}
        mode="carousel"
      />

      {/* Vertical Carousel */}
      <Swiper
        ref={swiperRef}
        direction="vertical"
        slidesPerView={1}
        spaceBetween={0}
        mousewheel={{
          thresholdDelta: 50,
          sensitivity: 1,
        }}
        keyboard={{
          enabled: true,
          onlyInViewport: true,
        }}
        pagination={{
          clickable: true,
          bulletClass: "swiper-pagination-bullet",
          bulletActiveClass: "swiper-pagination-bullet-active",
        }}
        speed={1000}
        modules={[Pagination, Mousewheel, Keyboard]}
        className="home-vertical-swiper"
        onSlideChange={handleSlideChange}
        style={{ height: "100vh" }}
      >
        <SwiperSlide id="hero-section">
          <Hero />
        </SwiperSlide>

        <SwiperSlide id="map-section">
          <MapProvider>
            <Map2 />
          </MapProvider>
        </SwiperSlide>

        <SwiperSlide id="services-section">
          <Services />
        </SwiperSlide>

        <SwiperSlide id="statistics-section">
          <PortalStatistics />
        </SwiperSlide>

        <SwiperSlide id="footer-section">
          <Footer />
        </SwiperSlide>
      </Swiper>
    </>
  );
}
