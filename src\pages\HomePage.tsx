import Hero from "../components/home/<USER>";
import Services from "../components/home/<USER>";
import PortalStatistics from "../components/home/<USER>";
// @ts-ignore
import { MapProvider } from "../components/mapFolder/MapContext.js";
import HomeLayout from "../components/layouts/HomeLayout";
import { useEffect } from "react";
import { scrollToTop } from "../utils/helpers";
import Map2 from "../components/home/<USER>/Map2.js";

import React, { useRef, useState } from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";

// // Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";

// import "./styles.css";

// import required modules
import { Pagination } from "swiper/modules";
import Navbar from "../components/Navbar.js";
import Footer from "../components/Footer.js";

/**
 * HomePage component that uses the HomeLayout
 * This page includes the Hero, Map, Services, and Statistics sections
 */
export default function HomePage() {
  // Scroll to top when the component mounts
  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    // <HomeLayout>
    //   <Hero />
    //   <MapProvider>
    //     <div>
    //       <Map2 />
    //     </div>
    //   </MapProvider>
    //   <Services />
    //   <PortalStatistics />
    // </HomeLayout>
    <>
      <Navbar onMenuToggle={() => {}} />
      <Swiper
        direction={"vertical"}
        pagination={{
          clickable: true,
        }}
        modules={[Pagination]}
        className="mySwiper"
      >
        <SwiperSlide>
          <Hero />
        </SwiperSlide>
        <SwiperSlide>
          <Map2 />
        </SwiperSlide>
        <SwiperSlide>
          <Services />
        </SwiperSlide>
        <SwiperSlide>
          <PortalStatistics />
        </SwiperSlide>
      </Swiper>
      <Footer />
    </>
  );
}
